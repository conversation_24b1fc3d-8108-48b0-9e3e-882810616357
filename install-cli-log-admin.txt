-------------------------------------------------------------------------------->8
Version: 4.43.1 (198352)
Sha1: 
Started on: 2025/07/06 15:03:55.972
Resources: C:\Program Files\Docker\Docker\resources
OS: Windows 10 Pro
Edition: Professional
Id: 2009
Build: 19045
BuildLabName: 19041.1.amd64fre.vb_release.191206-1406
File: C:\ProgramData\DockerDesktop\install-cli-log-admin.txt
CommandLine: "C:\Program Files\Docker\Docker\InstallerCli.exe" -i
You can send feedback, including this log file, at https://github.com/docker/for-win/issues
[2025-07-06T15:03:56.136674100Z][Installer][I] Preparing...
[2025-07-06T15:03:56.205053100Z][HyperVManager][I] Destroy
[2025-07-06T15:03:56.213294200Z][HyperVInfrastructure][I] Try to remove VM DockerDesktopVM
[2025-07-06T15:03:56.375375400Z][HyperVManager][W] Hyper-V is not installed on the machine
[2025-07-06T15:03:56.428388500Z][Installer][W] could not unregister distro docker-desktop: 

版权所有(c) Microsoft Corporation。保留所有权利。



用法: wsl.exe [Argument]



参数:



    --install <Options>

        安装适用于 Linux 的 Windows 子系统功能。如果未指定任何选项，

        则推荐的功能将与默认分发一起安装。



        若要查看默认分发以及其他有效分发的列表，

        请使用 "wsl --list --online"。



        选项:

            --distribution、-d [Argument]

                按名称指定要下载和安装的分发。



                参数:

                    有效的分发名称(不区分大小写)。

                    

                示例:

                    wsl --install -d Ubuntu

                    wsl --install --distribution Debian



            --inbox

                安装可选 Windows 功能，而不是 Microsoft Store 中提供的版本。



                        --no-distribution

                不安装分发(无法和 --distribution 一起使用)。



            --no-launch, -n

                安装后不启动发行版。



            --web-download

                从 Internet 上下载最新的 WSL 版本，而不是从 Microsoft Store 下载。



    --list, -l [Options]

        列出发行版。



        选项:

            --online, -o

                显示可用的发行版列表，以使用 'wsl --install' 安装。



    --status

        显示适用于 Linux 的 Windows 子系统的状态。



    --help

        显示使用情况信息。
[2025-07-06T15:03:56.428388500Z][Installer][I] Prepare completed successfully
[2025-07-06T15:03:56.428388500Z][Installer][I] Recreating Docker CLI plugins directory
[2025-07-06T15:03:56.429387800Z][Installer][I] Directory C:\Program Files\Docker\cli-plugins does not exist
[2025-07-06T15:03:56.430387800Z][Installer][I] Installing Docker CLI plugins
[2025-07-06T15:03:56.436394800Z][SymlinkUtils][W] Deleting folder C:\Users\<USER>\.docker\modules\tmp-delete\************************************
[2025-07-06T15:03:56.438394800Z][SymlinkUtils][W] Deleting folder C:\Users\<USER>\.docker\modules\tmp-delete
[2025-07-06T15:03:56.438394800Z][Installer][I] Re-linking docker-compose.exe
[2025-07-06T15:03:56.582038400Z][Installer][I] Setting permissions
[2025-07-06T15:03:56.582038400Z][Installer][I] Granting ReadAndExecute on C:\ProgramData\DockerDesktop
[2025-07-06T15:03:56.582038400Z][Installer][I] Permissions updated
