-------------------------------------------------------------------------------->8
Version: 4.43.1 (198352)
Sha1: 
Started on: 2025/07/06 13:20:33.955
Resources: F:\tools\resources
OS: Windows 10 Pro
Edition: Professional
Id: 1909
Build: 18363
BuildLabName: 18362.1.amd64fre.19h1_release.190318-1202
File: C:\ProgramData\DockerDesktop\install-log-admin.txt
CommandLine: "F:\tools\Docker_Desktop_Installer.exe" 
You can send feedback, including this log file, at https://github.com/docker/for-win/issues
[2025-07-06T13:20:34.072178300Z][Installer][I] No installation found
[2025-07-06T13:20:34.263755500Z][AdminInstallHandler][I] Running admin installation
[2025-07-06T13:20:34.385778500Z][InstallWorkflow][I] Using package: res:DockerDesktop
[2025-07-06T13:20:34.390774200Z][InstallWorkflow][I] Downloading
[2025-07-06T13:20:35.634988900Z][InstallWorkflow][I] Extracting manifest
[2025-07-06T13:20:36.251242200Z][InstallWorkflow][I] Manifest found: version=198352, displayVersion=4.43.1, channelUrl=https://desktop-stage.docker.com/win/main/amd64/appcast.xml
[2025-07-06T13:20:36.251242200Z][InstallWorkflow][I] Checking prerequisites
[2025-07-06T13:20:36.454461700Z][InstallWorkflow][E] Prerequisite failed: We've detected that you have an incompatible version of Windows.

Docker Desktop requires Windows 10 Pro/Enterprise/Home version 19044 or above.

To continue with the installation, upgrade Windows to a supported version and then re-run the installer.
[2025-07-06T13:20:36.472798900Z][Installer][W] Failed to track the installer started event, caused by: 发送请求时出错。
[2025-07-06T13:20:36.902173600Z][Installer][W] Failed to track the check for update info event, caused by: 发送请求时出错。
