-------------------------------------------------------------------------------->8
Version: 4.43.1 (198352)
Sha1: 
Started on: 2025/07/06 14:57:17.285
Resources: F:\tools\resources
OS: Windows 10 Pro
Edition: Professional
Id: 2009
Build: 19045
BuildLabName: 19041.1.amd64fre.vb_release.191206-1406
File: C:\ProgramData\DockerDesktop\install-log-admin.txt
CommandLine: "F:\tools\Docker_Desktop_Installer.exe" 
You can send feedback, including this log file, at https://github.com/docker/for-win/issues
[2025-07-06T14:57:17.410971000Z][Installer][I] No installation found
[2025-07-06T14:57:18.973567200Z][AdminInstallHandler][I] Running admin installation
[2025-07-06T14:57:19.286089300Z][InstallWorkflow][I] Using package: res:DockerDesktop
[2025-07-06T14:57:19.348593800Z][InstallWorkflow][I] Downloading
[2025-07-06T14:57:20.793840000Z][InstallWorkflow][I] Extracting manifest
[2025-07-06T14:57:21.476660200Z][InstallWorkflow][I] Manifest found: version=198352, displayVersion=4.43.1, channelUrl=https://desktop-stage.docker.com/win/main/amd64/appcast.xml
[2025-07-06T14:57:21.476660200Z][InstallWorkflow][I] Checking prerequisites
[2025-07-06T14:57:21.761724800Z][InstallWorkflow][I] Prompting for optional features
[2025-07-06T14:57:21.764725400Z][Installer][W] Failed to track the installer started event, caused by: 发送请求时出错。
[2025-07-06T15:01:58.094606600Z][InstallWorkflow][I] Selected backend mode: wsl-2
[2025-07-06T15:01:58.094606600Z][InstallWorkflow][I] Unpacking artifacts
[2025-07-06T15:03:43.992328400Z][InstallWorkflow][I] Deploying component Docker.Installer.CreateGroupAction
[2025-07-06T15:03:46.357597700Z][InstallWorkflow][I] Deploying component Docker.Installer.AddToGroupAction
[2025-07-06T15:03:48.654736100Z][InstallWorkflow][I] Deploying component Docker.Installer.EnableFeaturesAction
[2025-07-06T15:03:48.669740000Z][InstallWorkflow-EnableFeaturesAction][I] Required features: VirtualMachinePlatform, Microsoft-Windows-Subsystem-Linux
[2025-07-06T15:03:49.149905100Z][InstallWorkflow-EnableFeaturesAction][I] Enabling features: VirtualMachinePlatform, Microsoft-Windows-Subsystem-Linux
[2025-07-06T15:03:49.149905100Z][InstallWorkflow-EnableFeaturesAction][I] Enable Windows Feature VirtualMachinePlatform
[2025-07-06T15:03:49.149905100Z][InstallWorkflow-EnableFeaturesAction][I] Enable Windows Feature Microsoft-Windows-Subsystem-Linux
[2025-07-06T15:03:55.197052500Z][InstallWorkflow][I] Deploying component Docker.Installer.ServiceAction
[2025-07-06T15:03:55.199053000Z][InstallWorkflow-ServiceAction][I] Removing service
[2025-07-06T15:03:55.200053200Z][InstallWorkflow-ServiceAction][I] Creating service
[2025-07-06T15:03:55.202069600Z][InstallWorkflow][I] Deploying component Docker.Installer.ShortcutAction
[2025-07-06T15:03:55.210145300Z][InstallWorkflow-ShortcutAction][I] Creating shortcut: C:\ProgramData\Microsoft\Windows\Start Menu\Docker Desktop.lnk/Docker Desktop
[2025-07-06T15:03:55.213835000Z][InstallWorkflow][I] Deploying component Docker.Installer.AutoStartAction
[2025-07-06T15:03:55.216839600Z][InstallWorkflow][I] Deploying component Docker.Installer.PathAction
[2025-07-06T15:03:55.273616000Z][InstallWorkflow][I] Deploying component Docker.Installer.ExecAction
[2025-07-06T15:03:55.277617000Z][InstallWorkflow-ExecAction][I] Running: C:\Program Files\Docker\Docker\InstallerCli.exe -i with timeout=-1
[2025-07-06T15:03:56.622847500Z][InstallWorkflow][I] Registering product
[2025-07-06T15:03:56.624848000Z][RegisterProductStep][I] Creating installation manifest
[2025-07-06T15:03:56.624848000Z][RegisterProductStep][I] Registering product information
[2025-07-06T15:03:56.634850900Z][RegisterProductStep][I] Registering docker-desktop url-protocol
[2025-07-06T15:03:56.635851400Z][RegisterProductStep][I] Registering integration information
[2025-07-06T15:03:56.637852000Z][InstallWorkflow][I] Saving C:\ProgramData\DockerDesktop\install-settings.json
[2025-07-06T15:03:56.718870400Z][InstallWorkflow][I] Installation succeeded
[2025-07-06T15:03:57.138857500Z][Installer][W] Failed to track the installer finished event, caused by: 发送请求时出错。
